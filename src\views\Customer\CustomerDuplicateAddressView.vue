<script setup lang="ts">
import DuplicateAddressModal from '@/components/customer/DuplicateAddressModal.vue';
import { onMounted, ref } from 'vue';

const duplicateAddressModalRef = ref<InstanceType<typeof DuplicateAddressModal> | null>(null);

onMounted(() => {
  duplicateAddressModalRef.value?.openDuplicateAddressModal();
});
</script>

<template>
  <DuplicateAddressModal ref="duplicateAddressModalRef" />
</template>
