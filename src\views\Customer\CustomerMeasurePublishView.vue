<script setup lang="ts">
import { useRouter } from 'vue-router';
import { onMounted, ref } from 'vue';
import VueDatePicker, { type DatePickerInstance } from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { toastWarning } from '@/utils/toastification.ts';
import { AddressService } from '@/api/address.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { DatePickerToFullDateTimeWithDay, formatFullDateTimeWithDay, TimeToEmptyString } from '@/utils/timeFormat.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import HezDivider from '@/components/General/HezDivider.vue';
import {
  CustomerMeasureOrderDetailService,
  CustomerMeasureOrderService,
  CustomerOrderService
} from '@/api/customerOrder.ts';
import { StartUpService } from '@/api/startUp.ts';
import { useCustomerInfoStore, useStep1PublishTitleStore, customerStep1PublishStore } from '@/stores/customerGlobal.ts';
import { OrderTypeEnum } from '@/model/enum/orderType';

const router = useRouter();

const customerInfoStore = useCustomerInfoStore();
const titleStore = useStep1PublishTitleStore();
const step1Data = customerStep1PublishStore();
const customerStep1Ref = ref<boolean>(true);
const stepStatusMessage = ref<string>('');
const checkModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const multiAddressModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const customerMeasureData = ref<{
  address: {
    name: string;
    location: {
      lat: number;
      lng: number;
    };
  };
  username: string;
  measureTimes: string[];
}>({
  address: {
    name: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  username: '',
  measureTimes: []
});

const CheckInputOpenCheckInfoModal = () => {
  // 先檢查 customerMeasureData.measureTimes的陣列內 有沒有空字串的元素以及至少要有一個不是空字串的元數 沒做到就叫他填完整
  // 另外也要檢查 address.name 跟 username 有空的項目就叫他要填完整
  const hasValidMeasureTime = customerMeasureData.value.measureTimes.some((time) => time !== '');
  const hasEmptyMeasureTime = customerMeasureData.value.measureTimes.includes('');

  const isAddressEmpty = !customerMeasureData.value.address.name;
  const isUsernameEmpty = !customerMeasureData.value.username;

  if (isUsernameEmpty) {
    toastWarning('請填寫屋主姓名');
    return;
  }
  if (isAddressEmpty) {
    toastWarning('請填寫丈量地址');
    return;
  }
  if (!hasValidMeasureTime) {
    toastWarning('請填寫至少一個丈量時間');
    return;
  }
  if (hasEmptyMeasureTime) {
    toastWarning('丈量時間不可為空');
    return;
  }
  step1Data.updateData(customerMeasureData.value);
  // 檢查通過後才是打開確認資訊的Modal
  openCheckInfoModal();
};

const getFullAddress = async (address: string) => {
  // 拿 address call 取得完整地址 API 失敗或找不到的話要記得清空經緯度
  const response = await AddressService.AddressToAddressInfo({ address });
  if (response.status === APIStatusCodeEnum.Success) {
    customerMeasureData.value.address.name = response.result.fullName;
    customerMeasureData.value.address.location = response.result.location;
  } else {
    //TODO 我對不起未來後端的維護者 這項處理是我地址轉經緯度 沒法處理得很好 會有找不到地址的情形 跟這屆的後端討論過後 允許先給-999
    customerMeasureData.value.address.location = { lat: -999, lng: -999 };
  }
};

const goBack = () => {
  router.back();
};

const removeTimeSlot = (index: number) => {
  customerMeasureData.value.measureTimes.splice(index, 1);
};

const isValidTimeSlot = (newTime: Date, newIndex?: number): boolean => {
  for (let dataIndex = 0; dataIndex < customerMeasureData.value.measureTimes.length; dataIndex++) {
    // 忽略當前更新的元素和空字串的元素
    if (newIndex !== undefined) {
      if (dataIndex === newIndex || customerMeasureData.value.measureTimes[dataIndex] === '') continue;
    }
    const existingTime = new Date(customerMeasureData.value.measureTimes[dataIndex]);
    const diff = Math.abs(existingTime.getTime() - newTime.getTime());
    const sixHoursInMilliseconds = 6 * 60 * 60 * 1000;
    if (diff < sixHoursInMilliseconds) {
      return false;
    }
  }
  return true;
};

const timeAdd = (newTime: Date) => {
  if (isValidTimeSlot(newTime)) {
    customerMeasureData.value.measureTimes.push(newTime.toISOString());
  } else {
    toastWarning('間隔時間不得少於六小時。');
  }
  setTomorrowISOString();
};

const handleTimeUpdate = (newTime: Date | null, index: number) => {
  //這是按下時間選擇器的X會觸發的事件 就直接刪除該筆選擇器
  if (newTime === null) {
    removeTimeSlot(index);
    return;
  }

  if (isValidTimeSlot(newTime, index)) {
    customerMeasureData.value.measureTimes[index] = newTime.toISOString();
  } else {
    customerMeasureData.value.measureTimes[index] = '';
    removeTimeSlot(index); // 時間檢查不通過就直接刪除該筆資料
    toastWarning('間隔時間不得少於六小時。');
  }
};

const openCheckInfoModal = () => {
  checkModalRef.value?.openModal();
};

const closeCheckInfoModal = () => {
  checkModalRef.value?.closeModal();
};

const closeMultiAddressModal = () => {
  multiAddressModalRef.value?.closeModal();
};

const openMultiAddressModal = () => {
  multiAddressModalRef.value?.openModal();
};

const checkLoginMultiAddress = async () => {
  if (customerInfoStore.loginState) {
    // 已登入狀態去檢查是否有重複地址
    const response = await CustomerMeasureOrderDetailService.getManyOrderDetail({});
    if (response.status === APIStatusCodeEnum.Success) {
      let found = false;
      for (const item of response.result) {
        if (item.address === customerMeasureData.value?.address.name) {
          closeCheckInfoModal();
          // 跳轉到重複地址確認頁面而不是開啟 Modal
          await router.push({ name: 'customerduplicateaddress' });
          found = true;
          break;
        }
      }
      if (!found) {
        await submitCustomerMeasureData();
      }
    } else {
      console.error('error');
    }
  } else {
    await router.push({
      name: 'customerregister'
    });
  }
};

const submitCustomerMeasureData = async () => {
  // Call 刊登 API
  if (!customerMeasureData.value) return;
  const response = await CustomerMeasureOrderService.publishOrder(customerMeasureData.value);
  if (response.status === APIStatusCodeEnum.Success) {
    // 成功後立即綁定訂單
    const bindResponse = await CustomerMeasureOrderService.bindOrder({
      orderIds: [response.result.orderId]
    });
    if (bindResponse.status === APIStatusCodeEnum.Success) {
      // 成功後跳轉訂單成立Modal 清除pinia
      customerStep1PublishStore().cleanData();
      await router.push({
        name: 'customermeasurepublishok'
      });
    } else {
      console.error('error');
    }
  } else {
    console.error('error');
  }
};

const datePickAdd = ref<DatePickerInstance>(null);
//因為已選擇的時間段可能有多個 所以用陣列 不然觸發事件時會不知道要觸發哪一個DatePickerInstance
const datePickSelected = ref<DatePickerInstance[]>([]);

const selectedEditDate = (index: number) => {
  datePickSelected.value[index]?.selectDate();
};

const addSelectDate = () => {
  datePickAdd.value?.selectDate();
};

const selectedCloseMenu = (index: number) => {
  console.log('selected', datePickSelected.value);
  datePickSelected.value[index]?.closeMenu();
};

const addCloseMenu = () => {
  console.log('add', datePickAdd.value);
  datePickAdd.value?.closeMenu();
};

const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);

const tomorrowISOString = ref<string>('');

const setTomorrowISOString = () => {
  const tomorrowPreSelect = new Date();
  tomorrowPreSelect.setDate(tomorrowPreSelect.getDate() + 1);
  tomorrowPreSelect.setHours(14, 0, 0, 0); // 設定時間為14:00:00.000
  const offset = tomorrowPreSelect.getTimezoneOffset();
  const correctedTime = new Date(tomorrowPreSelect.getTime() - offset * 60 * 1000);
  tomorrowISOString.value = correctedTime.toISOString().slice(0, -1);
};

onMounted(async () => {
  customerMeasureData.value = step1Data.measureData;
  setTomorrowISOString();
  if (customerInfoStore.loginState) {
    const stepCheckResponse = await CustomerOrderService.StepCheck({ step: OrderTypeEnum.Measure });
    if (stepCheckResponse.status === APIStatusCodeEnum.Success) {
      customerStep1Ref.value = true;
    } else if (stepCheckResponse.status === APIStatusCodeEnum.StepFeatureClosed) {
      customerStep1Ref.value = false;
      stepStatusMessage.value = '敬請期待功能開放';
    } else if (stepCheckResponse.status === APIStatusCodeEnum.CustomerOrderDisabled) {
      customerStep1Ref.value = false;
      stepStatusMessage.value = '發單權限已關閉';
    }
  } else {
    const customerStep1 = (await StartUpService.getQuickInfo()).customerFeatures.customerStep1;
    if (customerStep1) {
      customerStep1Ref.value = true;
    } else {
      customerStep1Ref.value = false;
      stepStatusMessage.value = '敬請期待功能開放';
    }
  }
});
</script>

<template>
  <div v-if="!customerStep1Ref" class="fixed inset-0 z-50 flex items-center justify-center bg-black/60">
    <div class="flex flex-col items-center rounded-2xl bg-white px-10 py-8 shadow-2xl">
      <span class="mb-4 text-2xl font-bold">{{ stepStatusMessage }}</span>
      <button class="cus-btn w-full" @click="goBack()">確認</button>
    </div>
  </div>

  <div class="my-2 flex w-full flex-col gap-y-2">
    <div class="mx-auto flex flex-col p-4 text-start">
      <p class="text-2xl font-bold max-md:text-lg">{{ titleStore.title.mainTitle }}</p>
      <!--      <p class="font-bold text-2xl max-md:text-lg">{{ titleStore.title.subTitle }}</p>-->
      <p class="text-2xl font-bold max-md:text-lg">預約到府丈量服務</p>
      <p class="text-2xl font-bold max-md:text-lg">提供您專業室內平面圖和房屋健檢報告</p>
      <p class="text-2xl font-bold max-md:text-lg">原價2萬，優惠價2000元</p>
    </div>

    <div class="cus-border mx-auto flex w-full flex-col space-y-4 p-8 md:p-12">
      <div class="flex flex-col space-y-4">
        <p class="text-nowrap text-lg">屋主姓名</p>
        <input
          v-model="customerMeasureData.username"
          type="text"
          class="w-full rounded-xl border border-gray-500 p-2"
        />
      </div>
      <div class="flex flex-col space-y-4">
        <p class="text-nowrap text-lg">丈量地址</p>
        <input
          v-model="customerMeasureData.address.name"
          @change="getFullAddress(customerMeasureData.address.name)"
          type="text"
          class="w-full rounded-xl border border-gray-500 p-2"
        />
      </div>

      <div class="flex flex-col space-y-4">
        <p class="text-nowrap text-lg">丈量時間</p>
        <div class="flex gap-x-2 max-md:flex-col">
          <p class="">可預約時間為9:00~21:30</p>
          <p class="">每組預約時間間隔至少6小時</p>
        </div>
        <div v-for="(_, index) in customerMeasureData.measureTimes" :key="index" class="flex items-center">
          <VueDatePicker
            v-model="customerMeasureData.measureTimes[index]"
            :min-date="tomorrow"
            no-today
            no-hours-overlay
            no-minutes-overlay
            :format="DatePickerToFullDateTimeWithDay"
            :preview-format="TimeToEmptyString"
            disable-year-select
            placeholder="請選擇丈量時間"
            :enableTimePicker="true"
            :is24="true"
            locale="zh-TW"
            minutes-increment="30"
            :start-time="{ hours: 14, minutes: 0, seconds: 0 }"
            :min-time="{ hours: 9, minutes: 0, seconds: 0 }"
            :max-time="{ hours: 21, minutes: 30, seconds: 0 }"
            class="py-2"
            @update:model-value="(newTime) => handleTimeUpdate(newTime, index)"
            ref="datePickSelected"
          >
            <template #action-row>
              <div class="flex w-full justify-evenly space-x-3">
                <p
                  class="cus-border w-1/2 cursor-pointer px-4 py-2 text-center font-bold hover:bg-gray-200"
                  @click="selectedCloseMenu(index)"
                >
                  取消
                </p>
                <p
                  class="cus-border w-1/2 cursor-pointer px-4 py-2 text-center font-bold hover:bg-gray-200"
                  @click="selectedEditDate(index)"
                >
                  確定
                </p>
              </div>
            </template>
            <template #clock-icon>
              <p class="bg-color-selected cus-border w-full rounded-md p-1.5 font-bold text-black">選擇時間</p>
            </template>
            <template #calendar-icon>
              <p class="bg-color-selected cus-border w-full rounded-md p-1.5 font-bold text-black">選擇日期</p>
            </template>
          </VueDatePicker>
        </div>
        <VueDatePicker
          v-if="customerMeasureData.measureTimes.length < 3"
          v-model="tomorrowISOString"
          :min-date="tomorrow"
          no-today
          no-hours-overlay
          no-minutes-overlay
          :format="TimeToEmptyString"
          :preview-format="TimeToEmptyString"
          disable-year-select
          placeholder="請選擇丈量時間"
          :enableTimePicker="true"
          :is24="true"
          locale="zh-TW"
          minutes-increment="30"
          :start-time="{ hours: 14, minutes: 0, seconds: 0 }"
          :min-time="{ hours: 9, minutes: 0, seconds: 0 }"
          :max-time="{ hours: 21, minutes: 30, seconds: 0 }"
          class="py-2"
          @update:model-value="(newTime) => timeAdd(newTime)"
          ref="datePickAdd"
        >
          <template #action-row>
            <div class="flex w-full justify-evenly space-x-3">
              <p
                class="cus-border w-1/2 cursor-pointer px-4 py-2 text-center font-bold hover:bg-gray-200"
                @click="addCloseMenu"
              >
                取消
              </p>
              <p
                class="cus-border w-1/2 cursor-pointer px-4 py-2 text-center font-bold hover:bg-gray-200"
                @click="addSelectDate"
              >
                確定
              </p>
            </div>
          </template>
          <template #clock-icon>
            <p class="bg-color-selected cus-border w-full rounded-md p-1.5 font-bold text-black">選擇時間</p>
          </template>
          <template #calendar-icon>
            <p class="bg-color-selected cus-border w-full rounded-md p-1.5 font-bold text-black">選擇日期</p>
          </template>
        </VueDatePicker>
      </div>
      <div class="flex justify-between space-x-4 text-nowrap md:space-x-28 md:text-lg">
        <button type="button" class="cus-btn w-full" @click="goBack()">上一步</button>
        <button type="button" class="cus-btn w-full" @click="CheckInputOpenCheckInfoModal()">確認資訊</button>
      </div>
    </div>
  </div>

  <!--  確認預約資訊Modal-->
  <DefaultModal
    title="預約資訊"
    :show-close-button="true"
    :click-outside-close="false"
    modalWidth="max-w-lg"
    @closeModal="closeCheckInfoModal()"
    ref="checkModalRef"
  >
    <div class="m-3 flex-col text-black">
      <div class="mx-auto flex flex-col items-start justify-center">
        <div class="mb-1 flex flex-col items-start gap-y-3 text-left">
          <div class="mb-1 flex w-full items-center justify-start gap-x-3 text-left">
            <p class="font-bold">支付方式</p>
            <p class="flex-1">到府收取現金</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3 text-left">
            <p class="font-bold">總金額</p>
            <p class="flex-1">NT$ 2,000</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3 text-left">
            <p class="font-bold">屋主姓名</p>
            <p class="flex-1">{{ customerMeasureData.username }}</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3">
            <p class="font-bold">丈量地址</p>
            <p class="flex-1">{{ customerMeasureData.address.name }}</p>
          </div>
          <div class="mb-1 flex w-full items-start justify-start gap-x-3">
            <p class="font-bold">丈量時間</p>
            <div class="flex flex-1 flex-col gap-y-1">
              <div v-for="(time, index) in customerMeasureData.measureTimes" :key="index">
                <p>{{ formatFullDateTimeWithDay(time) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <HezDivider />
      <div class="mx-auto flex flex-col items-start justify-center">
        <div class="mb-1 flex flex-col items-start gap-y-3 text-left">
          <div class="mb-1 flex w-full items-center justify-start gap-x-3">
            <p class="font-bold">1.</p>
            <p class="flex-1">到府丈量服務後，您將會得到詳細的室內丈量圖，包含JPG、PDF、CAD。</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3">
            <p class="font-bold">2.</p>
            <p class="flex-1">您可以主動告知房屋的壁癌、漏水等相關資訊，家易會協助拍照並紀錄。</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3">
            <p class="font-bold">3.</p>
            <p class="flex-1">丈量服務費用可以用於折抵後續的室內設計費用。</p>
          </div>
          <div class="mb-1 flex w-full items-center justify-start gap-x-3">
            <p class="font-bold">4.</p>
            <p class="flex-1">
              原價20,000元，到App商店評價並留言，即可獲得<span class="font-bold text-red-600">特價2,000元</span>的優惠。
            </p>
          </div>
        </div>
      </div>
      <div class="text-color-secondary mt-1 md:gap-3">
        <button type="button" class="cus-btn w-full p-2" @click="checkLoginMultiAddress()">送出預約</button>
      </div>
    </div>
  </DefaultModal>

  <!--  已登入地址重複確認Modal-->
  <DefaultModal
    title="重複地址預約"
    :click-outside-close="false"
    :show-close-button="true"
    ref="multiAddressModalRef"
    @closeModal="closeMultiAddressModal()"
  >
    <div class="m-3 flex-col text-black">
      <p>您之前已經有預約丈量服務訂單</p>
      <p>您確定要使用相同地址重複預約?</p>
      <div class="text-color-secondary mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3">
        <button
          type="button"
          class="button-basic bg-color-selected w-full hover:opacity-80 md:col-start-2"
          @click="submitCustomerMeasureData()"
        >
          是
        </button>
        <button
          type="button"
          class="button-basic mt-1 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1 md:mt-0"
          @click="closeMultiAddressModal()"
        >
          否
        </button>
      </div>
    </div>
  </DefaultModal>
</template>

<style scoped></style>
